/**
 * Email verification template
 */

/**
 * Generates an email verification email
 * @param email The recipient's email
 * @param verificationLink The verification link
 * @param firstName Optional first name of the user
 * @returns HTML and subject for the email
 */
export function generateVerificationEmail(
  email: string, 
  verificationLink: string, 
  firstName?: string
): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined;
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,';

  const subject = 'Verify Your Email for Smile-Factory';

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/smile-factory-logo.svg" alt="Smile Factory" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Thank you for signing up for Smile-Factory! To complete your registration and access all features,
        please verify your email address by clicking the button below:
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${verificationLink}" style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Verify My Email
        </a>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        If the button doesn't work, you can also copy and paste the following link into your browser:
      </p>

      <p style="margin-bottom: 24px; word-break: break-all; background-color: #f5f5f5; padding: 12px; border-radius: 4px; font-family: monospace;">
        ${verificationLink}
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        This link will expire in 24 hours. If you didn't sign up for Smile-Factory, you can safely ignore this email.
      </p>

      <div style="margin-top: 32px; padding-top: 16px; border-top: 1px solid #eaeaea; color: #666; font-size: 12px;">
        <p>
          ZB Innovation Hub<br>
          <a href="https://zbinnovation.com" style="color: #0D8A3E;">zbinnovation.com</a>
        </p>
      </div>
    </div>
  `;

  return { html, subject };
}

/**
 * Extracts a name from an email address
 * @param email The email address
 * @returns The extracted name or undefined
 */
function extractNameFromEmail(email: string): string | undefined {
  if (!email || !email.includes('@')) return undefined;
  
  const localPart = email.split('@')[0];
  
  // Remove numbers and special characters
  const nameOnly = localPart.replace(/[0-9_\-.+]/g, ' ').trim();
  
  // If nothing left, return undefined
  if (!nameOnly) return undefined;
  
  // Capitalize first letter of each word
  return nameOnly
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
