// Supabase Auth Webhook Handler for user events
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'

// SendGrid API configuration
const SENDGRID_API_KEY = Deno.env.get('SENDGRID_API_KEY')
const SENDGRID_FROM_EMAIL = Deno.env.get('SENDGRID_FROM_EMAIL') || '<EMAIL>'
const SENDGRID_FROM_NAME = Deno.env.get('SENDGRID_FROM_NAME') || 'Smile-Factory'
const SITE_URL = Deno.env.get('SITE_URL') || 'https://Smile-Factory.co.zw'

// Function to extract name from email
function extractNameFromEmail(email: string): string | undefined {
  if (!email) return undefined

  // Get the part before the @ symbol
  const localPart = email.split('@')[0]

  // Remove numbers and special characters
  const cleanedName = localPart.replace(/[0-9_.-]/g, ' ')

  // Capitalize first letter of each word
  const words = cleanedName.split(' ')
    .filter(word => word.length > 0)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())

  // Return the first word if it exists
  return words.length > 0 ? words[0] : undefined
}

// Function to strip HTML tags for plain text version
function stripHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim()
}

// Function to send email via SendGrid
async function sendEmail(to: string, subject: string, html: string, text?: string): Promise<Response> {
  if (!SENDGRID_API_KEY) {
    throw new Error('SendGrid API key is not configured')
  }

  console.log(`Sending email to ${to} with subject "${subject}"`)

  const url = 'https://api.sendgrid.com/v3/mail/send'
  const plainText = text || stripHtml(html)

  const payload = {
    personalizations: [
      {
        to: [{ email: to }],
        subject: subject
      }
    ],
    from: {
      email: SENDGRID_FROM_EMAIL,
      name: SENDGRID_FROM_NAME
    },
    content: [
      {
        type: 'text/plain',
        value: plainText
      },
      {
        type: 'text/html',
        value: html
      }
    ]
  }

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${SENDGRID_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  })

  if (!response.ok) {
    let errorMessage = `Failed to send email: ${response.status} ${response.statusText}`
    try {
      const errorData = await response.json()
      errorMessage = `${errorMessage} - ${JSON.stringify(errorData)}`
    } catch (e) {
      // Ignore JSON parsing error
    }
    throw new Error(errorMessage)
  }

  return response
}

// Function to generate welcome email
function generateWelcomeEmail(email: string, firstName?: string): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,'

  const subject = 'Welcome to ZbInnovation'

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZbInnovation" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Thank you for joining the ZbInnovation platform. We're excited to have you on board!
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Our platform connects innovators, mentors, organizations, and academic institutions to foster collaboration and drive innovation in Zimbabwe.
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        To get started, please complete your profile to unlock all the features of our platform:
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${SITE_URL}/dashboard" 
           style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin-right: 10px;">
          Go to Dashboard
        </a>
        <a href="${SITE_URL}/dashboard/profile" 
           style="background-color: #a4ca39; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Complete Profile
        </a>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        If you have any questions or need assistance, please don't hesitate to contact our support team.
      </p>

      <p style="margin-bottom: 8px; line-height: 1.5;">
        Best regards,
      </p>

      <p style="line-height: 1.5;">
        The ZbInnovation Team
      </p>

      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>
          © ${new Date().getFullYear()} ZbInnovation. All rights reserved.
        </p>
        <p>
          This email was sent to ${email}
        </p>
      </div>
    </div>
  `

  return { html, subject }
}

// Main handler function
serve(async (req) => {
  console.log('Auth Webhook Function invoked:', new Date().toISOString())

  // Handle CORS for preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse the webhook payload
    const payload = await req.json()
    console.log('Received webhook payload:', JSON.stringify(payload))

    // Extract user data from the webhook payload
    // The structure depends on how Supabase sends the webhook data
    // This is the standard format for Supabase Auth webhooks
    const user = payload.record || payload.user
    
    if (!user || !user.email) {
      console.error('Invalid webhook payload - missing user data')
      return new Response(
        JSON.stringify({ error: 'Invalid webhook payload - missing user data' }),
        { 
          status: 400,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      )
    }

    // Generate welcome email
    const email = user.email
    const firstName = user.user_metadata?.first_name || undefined
    const emailContent = generateWelcomeEmail(email, firstName)

    // Send the welcome email
    await sendEmail(
      email,
      emailContent.subject,
      emailContent.html
    )

    // Return success response
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Welcome email sent to ${email}` 
      }),
      {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  } catch (error) {
    console.error('Error processing webhook:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Failed to process webhook' 
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  }
})
