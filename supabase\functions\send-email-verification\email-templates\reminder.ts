/**
 * Profile completion reminder email template
 */

/**
 * Generates a profile completion reminder email
 * @param email The recipient's email
 * @param profileLink The link to complete the profile
 * @param firstName Optional first name of the user
 * @param profileCompletion Optional profile completion percentage
 * @returns HTML and subject for the email
 */
export function generateProfileReminderEmail(
  email: string, 
  profileLink: string, 
  firstName?: string,
  profileCompletion?: number
): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined;
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,';

  const completionText = profileCompletion !== undefined 
    ? `Your profile is currently ${profileCompletion}% complete.` 
    : 'Your profile is not yet complete.';

  const subject = 'Complete Your ZB Innovation Hub Profile';

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="Smile-Factory" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Thank you for joining Smile-Factory! We noticed that you haven't completed your profile yet.
        ${completionText}
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        A complete profile helps us connect you with the right opportunities and resources.
        It also makes your profile more visible to other members of our community.
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${profileLink}" style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Complete My Profile
        </a>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        If the button doesn't work, you can also copy and paste the following link into your browser:
      </p>

      <p style="margin-bottom: 24px; word-break: break-all; background-color: #f5f5f5; padding: 12px; border-radius: 4px; font-family: monospace;">
        ${profileLink}
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        If you have any questions or need assistance, please don't hesitate to contact our support team.
      </p>

      <div style="margin-top: 32px; padding-top: 16px; border-top: 1px solid #eaeaea; color: #666; font-size: 12px;">
        <p>
          ZB Innovation Hub<br>
          <a href="https://zbinnovation.com" style="color: #0D8A3E;">zbinnovation.com</a>
        </p>
        <p>
          If you no longer wish to receive these reminders, you can update your notification preferences in your account settings.
        </p>
      </div>
    </div>
  `;

  return { html, subject };
}

/**
 * Extracts a name from an email address
 * @param email The email address
 * @returns The extracted name or undefined
 */
function extractNameFromEmail(email: string): string | undefined {
  if (!email || !email.includes('@')) return undefined;
  
  const localPart = email.split('@')[0];
  
  // Remove numbers and special characters
  const nameOnly = localPart.replace(/[0-9_\-.+]/g, ' ').trim();
  
  // If nothing left, return undefined
  if (!nameOnly) return undefined;
  
  // Capitalize first letter of each word
  return nameOnly
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
