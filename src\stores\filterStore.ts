import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useFilterStore = defineStore('filters', () => {
  // Base state for AI-powered filtering
  const activeTab = ref('feed');
  const aiSearchActive = ref(false);
  const lastAIQuery = ref('');
  const aiSearchResults = ref([]);
  const aiSearchContext = ref('');

  // Track AI trigger usage for analytics
  const aiTriggerHistory = ref<Array<{
    triggerKey: string;
    context: string;
    timestamp: Date;
    success: boolean;
  }>>([]);

  // Computed properties for AI-powered filtering
  const currentAIContext = computed(() => {
    return `community-${activeTab.value}`;
  });

  const hasActiveAISearch = computed(() => {
    return aiSearchActive.value && lastAIQuery.value.length > 0;
  });

  const recentAITriggers = computed(() => {
    return aiTriggerHistory.value
      .filter(trigger => trigger.success)
      .slice(-5) // Last 5 successful triggers
      .reverse();
  });

  // Actions for AI-powered filtering
  function setActiveTab(tab: string) {
    console.log(`AI Filter Store: Setting active tab to ${tab}`);
    activeTab.value = tab;
    // Clear any active AI search when switching tabs
    clearAISearch();
  }

  function startAISearch(query: string, context: string) {
    console.log(`AI Filter Store: Starting AI search - Query: ${query}, Context: ${context}`);
    aiSearchActive.value = true;
    lastAIQuery.value = query;
    aiSearchContext.value = context;
  }

  function setAISearchResults(results: any[]) {
    console.log(`AI Filter Store: Setting AI search results - Count: ${results.length}`);
    aiSearchResults.value = results;
  }

  function clearAISearch() {
    console.log('AI Filter Store: Clearing AI search');
    aiSearchActive.value = false;
    lastAIQuery.value = '';
    aiSearchResults.value = [];
    aiSearchContext.value = '';
  }

  function recordAITrigger(triggerKey: string, context: string, success: boolean) {
    console.log(`AI Filter Store: Recording AI trigger - ${triggerKey} (${success ? 'success' : 'failed'})`);
    aiTriggerHistory.value.push({
      triggerKey,
      context,
      timestamp: new Date(),
      success
    });

    // Keep only last 50 triggers to prevent memory issues
    if (aiTriggerHistory.value.length > 50) {
      aiTriggerHistory.value = aiTriggerHistory.value.slice(-50);
    }
  }

  function getAITriggerStats() {
    const total = aiTriggerHistory.value.length;
    const successful = aiTriggerHistory.value.filter(t => t.success).length;
    const failed = total - successful;

    return {
      total,
      successful,
      failed,
      successRate: total > 0 ? (successful / total) * 100 : 0
    };
  }

  // Reset all AI filter state
  function resetAIFilters() {
    console.log('AI Filter Store: Resetting all AI filters');
    clearAISearch();
    aiTriggerHistory.value = [];
  }

  return {
    // State
    activeTab,
    aiSearchActive,
    lastAIQuery,
    aiSearchResults,
    aiSearchContext,
    aiTriggerHistory,

    // Computed
    currentAIContext,
    hasActiveAISearch,
    recentAITriggers,

    // Actions
    setActiveTab,
    startAISearch,
    setAISearchResults,
    clearAISearch,
    recordAITrigger,
    getAITriggerStats,
    resetAIFilters
  };
});
